<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <radialGradient id="sunGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffff00;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#ff8800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff4400;stop-opacity:0.8" />
    </radialGradient>
  </defs>
  
  <!-- Background space -->
  <rect width="100" height="100" fill="#000011"/>
  
  <!-- Stars -->
  <circle cx="15" cy="20" r="0.5" fill="white" opacity="0.8"/>
  <circle cx="85" cy="15" r="0.3" fill="white" opacity="0.6"/>
  <circle cx="90" cy="80" r="0.4" fill="white" opacity="0.7"/>
  <circle cx="20" cy="85" r="0.3" fill="white" opacity="0.5"/>
  
  <!-- Sun -->
  <circle cx="50" cy="50" r="15" fill="url(#sunGradient)"/>
  
  <!-- Planets (simplified) -->
  <circle cx="35" cy="50" r="1.5" fill="#8c7853"/> <!-- Mercury -->
  <circle cx="65" cy="50" r="2" fill="#ffc649"/> <!-- Venus -->
  <circle cx="75" cy="50" r="2.2" fill="#6b93d6"/> <!-- Earth -->
  
  <!-- Orbit lines -->
  <circle cx="50" cy="50" r="25" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/>
  <circle cx="50" cy="50" r="35" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/>
</svg>
